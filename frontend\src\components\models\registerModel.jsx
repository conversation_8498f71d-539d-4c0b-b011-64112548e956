import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import {
  X,
  Check,
  Lock,
  LockOpen,
  Coins,
  Wallet,
  Receipt,
  Printer,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useRegister } from "../../context/RegisterContext";
import { useToasts } from "../../context/ToastContext";

const RegisterModal = ({ isOpen, onClose, user, mode = "auto" }) => {
  const {
    registerStatus,
    openRegister,
    closeRegister,
    updateOpeningCash,
    closePreviousDayRegister,
    loading,
    error: registerError,
    terminalId,
    refreshRegisterStatus,
    isNewDay,
    initializeDailyRegister,
    getCurrentDateString,
    getAuthHeaders, // <-- added for API auth
  } = useRegister();

  const { addToast } = useToasts();
  const [inputAmount, setInputAmount] = useState("");
  const [otherAmount, setOtherAmount] = useState("");
  const [localIsOpen, setLocalIsOpen] = useState(isOpen);
  const inputRef = useRef(null);
  const [errorMessage, setErrorMessage] = useState("");
  const [showReportPreview, setShowReportPreview] = useState(false);
  const [addToCurrent, setAddToCurrent] = useState(false);
  const [showUnclosedDialog, setShowUnclosedDialog] = useState(false);
  const [unclosedRegisterData, setUnclosedRegisterData] = useState(null);
  const [detailedTransactionData, setDetailedTransactionData] = useState(null);
  const [loadingTransactionData, setLoadingTransactionData] = useState(false);

  // Calculate the preview amount for real-time display
  const getPreviewAmount = () => {
    const amount = parseFloat(inputAmount) || 0;
    if (isUpdatingCash && addToCurrent) {
      return Number(registerStatus.openingCash || 0) + amount;
    } else if (isUpdatingCash && !addToCurrent) {
      return amount;
    } else if (!isClosing && addToCurrent && registerStatus.dailyInitialized) {
      return Number(registerStatus.openingCash || 0) + amount;
    }
    return amount;
  };

  // Determine the operation mode
  const isClosing = mode === "close" || (mode === "auto" && registerStatus.isOpen);
  const isUpdatingCash = mode === "updateCash";
  const isOpening = mode === "open" || (mode === "auto" && !registerStatus.isOpen);
  const isDailyInitialization = !registerStatus.dailyInitialized && !isClosing;

  useEffect(() => {
    setLocalIsOpen(isOpen);

    // Check for unclosed previous day register when modal opens
    if (isOpen && registerStatus.unclosedPreviousDay && !isClosing) {
      setUnclosedRegisterData(registerStatus.unclosedPreviousDay);
      setShowUnclosedDialog(true);
    }

    // Auto-fetch detailed transaction data when closing register or updating cash
    if (isOpen && registerStatus.registerId && (isClosing || isUpdatingCash)) {
      fetchDetailedTransactionData();
    }
  }, [isOpen, registerStatus.unclosedPreviousDay, isClosing, registerStatus.registerId]);

  useEffect(() => {
    if (localIsOpen && !showUnclosedDialog) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }
  }, [localIsOpen, showUnclosedDialog]);

  useEffect(() => {
    if (isClosing && localIsOpen) {
      // Don't auto-fill the cash amount - leave it empty for manual entry
      setInputAmount("");
      setOtherAmount("");
    } else if (!isClosing && localIsOpen) {
      // For updateCash mode, don't pre-fill - let user enter the amount they want to add/replace
      if (isUpdatingCash) {
        setInputAmount("");
      } else {
        setInputAmount("");
      }
      setOtherAmount("");
    }
  }, [isClosing, localIsOpen, isUpdatingCash, registerStatus.openingCash]);

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleConfirm();
    }
  };

  const handleConfirm = async () => {
    const amount = parseFloat(inputAmount);
    const other = parseFloat(otherAmount || 0);

    if (isNaN(amount) || amount < 0) {
      setErrorMessage("Please enter a valid non-negative amount");
      return;
    }

    if (isClosing && (isNaN(other) || other < 0)) {
      setErrorMessage("Please enter a valid non-negative other amount");
      return;
    }

    setErrorMessage("");

    try {
      let result;
      if (isClosing) {
        result = await closeRegister({
          inCashierAmount: amount,
          otherAmount: other,
        });
      } else if (isUpdatingCash) {
        // Update opening cash amount for already open register
        result = await updateOpeningCash(amount, addToCurrent);
      } else {
        // Open register with new opening cash (daily initialization or first time)
        result = await openRegister({
          user_id: user.id,
          terminal_id: terminalId,
          opening_cash: amount,
          add_to_current: addToCurrent && registerStatus.dailyInitialized,
        });
      }

      if (result?.success) {
        const finalAmount = result.finalAmount || amount;
        addToast(
          isClosing
            ? `Register closed with LKR ${Number(amount).toFixed(2)} in cash. Total sales: LKR ${Number(registerStatus.totalSales || 0).toFixed(2)}`
            : isUpdatingCash
            ? addToCurrent
              ? `Added LKR ${Number(amount).toFixed(2)} to opening cash. New total: LKR ${Number(finalAmount).toFixed(2)}.`
              : `Opening cash amount updated to LKR ${Number(finalAmount).toFixed(2)}.`
            : addToCurrent && registerStatus.dailyInitialized
            ? `Added LKR ${Number(amount).toFixed(2)} to opening cash. New total: LKR ${Number(finalAmount).toFixed(2)}.`
            : `Register opened with LKR ${Number(finalAmount).toFixed(2)} starting cash.`,
          {
            autoClose: 5000,
          }
        );

        // Clear input fields and reset state
        setInputAmount("");
        setOtherAmount("");
        setAddToCurrent(false);
        setErrorMessage("");

        onClose();
      } else {
        setErrorMessage(
          result?.error || "Failed to process register operation"
        );
      }
    } catch (error) {
      setErrorMessage("An unexpected error occurred. Please try again.");
      console.error("Register operation failed:", error);
    }
  };

  const handleContinuePreviousRegister = async () => {
    const amount = parseFloat(inputAmount) || 0;

    try {
      const result = await openRegister({
        user_id: user.id,
        terminal_id: terminalId,
        opening_cash: amount,
        continue_previous: true,
        previous_register_id: unclosedRegisterData.id,
      });

      if (result?.success) {
        addToast(
          `Continued previous register with additional LKR ${Number(amount).toFixed(2)}. Total opening cash: LKR ${Number(result.finalAmount || 0).toFixed(2)}`,
          { autoClose: 5000 }
        );
        setShowUnclosedDialog(false);
        setUnclosedRegisterData(null);
        onClose();
      } else {
        setErrorMessage(result?.error || "Failed to continue previous register");
      }
    } catch (error) {
      setErrorMessage("An unexpected error occurred");
      console.error("Continue previous register failed:", error);
    }
  };

  const handleClosePreviousRegister = async () => {
    const closingAmount = parseFloat(inputAmount) || 0;

    try {
      const result = await closePreviousDayRegister(
        unclosedRegisterData.id,
        closingAmount,
        closingAmount
      );

      if (result?.success) {
        addToast(
          `Previous day register closed successfully`,
          { autoClose: 5000 }
        );
        setShowUnclosedDialog(false);
        setUnclosedRegisterData(null);
        // Reset for new register opening
        setInputAmount("");
      } else {
        setErrorMessage(result?.error || "Failed to close previous register");
      }
    } catch (error) {
      setErrorMessage("An unexpected error occurred");
      console.error("Close previous register failed:", error);
    }
  };

  const handleStartFreshRegister = () => {
    setShowUnclosedDialog(false);
    setUnclosedRegisterData(null);
    setInputAmount("");
  };

  const fetchDetailedTransactionData = async () => {
    if (!registerStatus.registerId) return;
    
    setLoadingTransactionData(true);
    try {
      const response = await axios.get(`/api/register/${registerStatus.registerId}/detailed-summary`, getAuthHeaders());
      setDetailedTransactionData(response.data);
    } catch (error) {
      console.error("Failed to fetch detailed transaction data:", error);
      // Continue without detailed data
    } finally {
      setLoadingTransactionData(false);
    }
  };

  const handlePrintReport = () => {
    setShowReportPreview(true);
    // Actual printing would be handled after user confirms the preview
  };

  const printReportContent = () => {
    const printWindow = window.open("", "_blank");
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Register Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
          .report-header { text-align: center; margin-bottom: 20px; }
          .report-title { font-size: 18px; font-weight: bold; }
          .report-subtitle { font-size: 14px; color: #555; }
          .report-body { margin-top: 15px; }
          .report-row { display: flex; justify-content: space-between; padding: 5px 0; }
          .report-label { font-weight: bold; }
          .report-divider { border-top: 1px dashed #000; margin: 10px 0; }
          .report-footer { margin-top: 20px; font-size: 12px; text-align: center; }
          @media print {
            @page { size: auto; margin: 5mm; }
          }
        </style>
      </head>
      <body>
        <div class="report-header">
          <div class="report-title">REGISTER CLOSING REPORT</div>
          <div class="report-subtitle">${new Date().toLocaleString()}</div>
        </div>
        
        <div class="report-body">
          <div class="report-row">
            <span>Terminal ID:</span>
            <span>${terminalId}</span>
          </div>
          <div class="report-row">
            <span>User:</span>
            <span>${user.name}</span>
          </div>
          
          <div class="report-divider"></div>
          
          <div class="report-row">
            <span class="report-label">Opening Cash:</span>
            <span class="report-label">LKR ${Number(registerStatus.openingCash || 0).toFixed(2)}</span>
          </div>
          <div class="report-row">
            <span>Total Sales:</span>
            <span>LKR ${Number(registerStatus.totalSales || 0).toFixed(2)}</span>
          </div>
          <div class="report-row">
            <span>Total Quantity:</span>
            <span>${registerStatus.totalSalesQty || "0"}</span>
          </div>
          
          <div class="report-divider"></div>
          
          <div class="report-row">
            <span class="report-label">Cash in Register:</span>
            <span class="report-label">LKR ${inputAmount || "0.00"}</span>
          </div>
          <div class="report-row">
            <span>Other Amount:</span>
            <span>LKR ${otherAmount || "0.00"}</span>
          </div>
          
          <div class="report-divider"></div>
          
          <div class="report-row">
            <span class="report-label">Total Expected:</span>
            <span class="report-label">
              LKR ${(parseFloat(registerStatus.openingCash || 0) + parseFloat(registerStatus.totalSales || 0)).toFixed(2)}
            </span>
          </div>
        </div>
        
        <div class="report-footer">
          <div>System generated report - ${new Date().toLocaleDateString()}</div>
          <div>Thank you!</div>
        </div>
        
        <script>
          window.onload = function() {
            setTimeout(function() {
              window.print();
              window.close();
            }, 200);
          }
        </script>
      </body>
      </html>
    `);
    printWindow.document.close();
  };

  if (!localIsOpen) return null;

  // Show unclosed register dialog first
  if (showUnclosedDialog && unclosedRegisterData) {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm"
        >
          <motion.div
            initial={{ y: 50, scale: 0.95, opacity: 0 }}
            animate={{ y: 0, scale: 1, opacity: 1 }}
            exit={{ y: 50, scale: 0.95, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="relative w-full max-w-md p-6 overflow-hidden border border-orange-100 shadow-2xl bg-gradient-to-br from-orange-50 to-red-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl dark:border-gray-700"
          >
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 rounded-full shadow-inner dark:bg-orange-900">
                    <Lock className="text-orange-600 dark:text-orange-300" size={24} />
                  </div>
                  <h2 className="text-xl font-bold text-gray-800 dark:text-white">
                    Unclosed Register Found
                  </h2>
                </div>
                <button
                  onClick={() => {
                    setShowUnclosedDialog(false);
                    onClose();
                  }}
                  className="p-2 bg-white rounded-full shadow dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  <X size={20} className="text-gray-600 dark:text-gray-300" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="p-4 bg-white border border-orange-200 shadow-sm dark:bg-gray-700 rounded-xl dark:border-gray-600">
                  <p className="mb-3 text-gray-700 dark:text-gray-200">
                    You have an unclosed register from{" "}
                    <span className="font-semibold text-orange-600 dark:text-orange-400">
                      {unclosedRegisterData.date}
                    </span>{" "}
                    with opening balance of{" "}
                    <span className="font-semibold">
                      LKR {Number(unclosedRegisterData.opening_balance || 0).toFixed(2)}
                    </span>
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    What would you like to do?
                  </p>
                </div>

                <div className="space-y-3">
                  <label className="flex items-center gap-2 mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                    <Coins className="text-blue-500 dark:text-blue-400" size={16} />
                    Additional Opening Cash (LKR)
                  </label>
                  <input
                    ref={inputRef}
                    type="number"
                    value={inputAmount}
                    onChange={(e) => setInputAmount(e.target.value)}
                    className="w-full p-3 transition-all bg-white border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:focus:ring-blue-400 dark:bg-gray-800 dark:text-white"
                    placeholder="Enter additional amount (optional)"
                  />
                </div>

                {errorMessage && (
                  <div className="text-sm text-center text-red-500">
                    {errorMessage}
                  </div>
                )}

                <div className="grid grid-cols-1 gap-3">
                  <button
                    onClick={handleContinuePreviousRegister}
                    disabled={loading}
                    className="flex items-center justify-center gap-2 p-3 text-white bg-green-600 rounded-xl hover:bg-green-700 disabled:opacity-50"
                  >
                    <Check size={18} />
                    Continue Previous Register
                  </button>

                  <button
                    onClick={handleClosePreviousRegister}
                    disabled={loading}
                    className="flex items-center justify-center gap-2 p-3 text-white bg-orange-600 rounded-xl hover:bg-orange-700 disabled:opacity-50"
                  >
                    <Lock size={18} />
                    Close Previous & Continue
                  </button>

                  <button
                    onClick={handleStartFreshRegister}
                    disabled={loading}
                    className="flex items-center justify-center gap-2 p-3 text-gray-800 bg-white border border-gray-200 shadow-sm dark:bg-gray-700 dark:text-white rounded-xl hover:bg-gray-50 dark:hover:bg-gray-600 dark:border-gray-600"
                  >
                    <LockOpen size={18} />
                    Start Fresh Register
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </AnimatePresence>
    );
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm"
      >
        <motion.div
          initial={{ y: 50, scale: 0.95, opacity: 0 }}
          animate={{ y: 0, scale: 1, opacity: 1 }}
          exit={{ y: 50, scale: 0.95, opacity: 0 }}
          transition={{ type: "spring", damping: 20, stiffness: 300 }}
          className="relative w-full max-w-md p-6 overflow-hidden border border-blue-100 shadow-2xl bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl dark:border-gray-700"
        >
          <motion.div
            animate={{
              x: [0, 5, -5, 0],
              y: [0, -5, 5, 0],
              rotate: [0, 5, -5, 0],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              ease: "linear",
            }}
            className="absolute w-40 h-40 bg-blue-200 rounded-full -top-20 -right-20 dark:bg-blue-900 opacity-20"
          />
          <motion.div
            animate={{
              x: [0, -5, 5, 0],
              y: [0, 5, -5, 0],
              rotate: [0, -5, 5, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear",
              delay: 2,
            }}
            className="absolute w-40 h-40 bg-indigo-200 rounded-full -bottom-20 -left-20 dark:bg-indigo-900 opacity-20"
          />
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{
                    rotateY: isClosing ? 180 : 0,
                    scale: [1, 1.1, 1],
                  }}
                  transition={{
                    rotateY: { duration: 0.5 },
                    scale: { duration: 0.3, delay: 0.2 },
                  }}
                  className="p-2 bg-blue-100 rounded-full shadow-inner dark:bg-blue-900"
                >
                  {isClosing ? (
                    <Lock
                      className="text-blue-600 dark:text-blue-300"
                      size={24}
                    />
                  ) : (
                    <LockOpen
                      className="text-blue-600 dark:text-blue-300"
                      size={24}
                    />
                  )}
                </motion.div>
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                  {isClosing
                    ? "Close Register"
                    : isUpdatingCash
                    ? "Update Opening Cash"
                    : isDailyInitialization
                    ? "Daily Register Setup"
                    : "Add Opening Cash"}
                </h2>
              </div>
              <motion.button
                whileHover={{ scale: 1.1, rotate: 90 }}
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                className="p-2 bg-white rounded-full shadow dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                <X size={20} className="text-gray-600 dark:text-gray-300" />
              </motion.button>
            </div>

            {showReportPreview ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="p-4 bg-white shadow-lg dark:bg-gray-800 rounded-xl"
              >
                <h3 className="mb-4 text-lg font-bold text-center">
                  Register Closing Report
                </h3>

                <div className="mb-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium">Terminal ID:</span>
                    <span>{terminalId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">User:</span>
                    <span>{user.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Date:</span>
                    <span>{new Date().toLocaleString()}</span>
                  </div>

                  <div className="my-2 border-t border-gray-300 border-dashed"></div>

                  <div className="flex justify-between">
                    <span className="font-medium">Opening Cash:</span>
                    <span>
                      LKR {Number(registerStatus.openingCash || 0).toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Total Sales:</span>
                    <span>
                      LKR {Number(registerStatus.totalSales || 0).toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Total Quantity:</span>
                    <span>{registerStatus.totalSalesQty || "0"}</span>
                  </div>

                  <div className="my-2 border-t border-gray-300 border-dashed"></div>

                  <div className="flex justify-between font-bold">
                    <span>Cash in Register:</span>
                    <span>LKR {inputAmount || "0.00"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Other Amount:</span>
                    <span>LKR {otherAmount || "0.00"}</span>
                  </div>

                  <div className="my-2 border-t border-gray-300 border-dashed"></div>

                  <div className="flex justify-between text-lg font-bold">
                    <span>Total Expected:</span>
                    <span>
                      LKR{" "}
                      {(
                        parseFloat(registerStatus.openingCash || 0) +
                        parseFloat(registerStatus.totalSales || 0)
                      ).toFixed(2)}
                    </span>
                  </div>
                </div>

                <div className="flex gap-3 mt-4">
                  <button
                    onClick={() => setShowReportPreview(false)}
                    className="flex-1 py-2 bg-gray-200 rounded-lg hover:bg-gray-300"
                  >
                    Back
                  </button>
                  <button
                    onClick={() => {
                      printReportContent();
                      setShowReportPreview(false);
                    }}
                    className="flex items-center justify-center flex-1 gap-2 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                  >
                    <Printer size={16} /> Print
                  </button>
                </div>
              </motion.div>
            ) : (
              <div className="space-y-6">
                {isClosing ? (
                  <>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                      className="p-4 bg-white border border-gray-100 shadow-sm dark:bg-gray-700 rounded-xl dark:border-gray-600"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="flex items-center gap-2 font-semibold text-gray-700 dark:text-gray-200">
                          <Receipt
                            className="text-blue-500 dark:text-blue-400"
                            size={18}
                          />
                          Closing Summary
                        </h3>
                        <button
                          onClick={fetchDetailedTransactionData}
                          disabled={loadingTransactionData}
                          className="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 disabled:opacity-50"
                        >
                          {loadingTransactionData ? "Loading..." : "Refresh Details"}
                        </button>
                      </div>
                      
                      {/* Basic Summary */}
                      {loadingTransactionData && (
                        <div className="mb-3 text-center">
                          <div className="inline-flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
                            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                            Loading transaction details...
                          </div>
                        </div>
                      )}
                      <div className="grid grid-cols-2 gap-3 mb-4">
                        <div className="text-gray-600 dark:text-gray-400">
                          Total Sales:
                        </div>
                        <div className="font-medium text-right dark:text-white">
                          LKR {Number(registerStatus.totalSales || 0).toFixed(2)}
                        </div>
                        <div className="text-gray-600 dark:text-gray-400">
                          Total Quantity:
                        </div>
                        <div className="font-medium text-right dark:text-white">
                          {registerStatus.totalSalesQty || "0"}
                        </div>
                        <div className="text-gray-600 dark:text-gray-400">
                          Opening Cash:
                        </div>
                        <div className="font-medium text-right dark:text-white">
                          LKR {Number(registerStatus.openingCash || 0).toFixed(2)}
                        </div>
                        {detailedTransactionData?.loyalty_redemptions?.total > 0 && (
                          <>
                            <div className="text-gray-600 dark:text-gray-400">
                              Loyalty Points Redeemed:
                            </div>
                            <div className="font-medium text-right text-green-600 dark:text-green-400">
                              LKR {Number(detailedTransactionData.loyalty_redemptions.total).toFixed(2)}
                              {detailedTransactionData.loyalty_redemptions.count > 0 && (
                                <span className="ml-2 text-xs text-gray-500">
                                  ({detailedTransactionData.loyalty_redemptions.count} transactions)
                                </span>
                              )}
                            </div>
                            {detailedTransactionData?.loyalty_redemptions?.count > 0 && (
                              <>
                                <div className="text-gray-600 dark:text-gray-400">
                                  Loyalty Redemption Transactions:
                                </div>
                                <div className="font-medium text-right text-green-600 dark:text-green-400">
                                  {detailedTransactionData.loyalty_redemptions.count}
                                </div>
                              </>
                            )}
                          </>
                        )}
                        {detailedTransactionData?.free_items?.total_value > 0 && (
                          <>
                            <div className="text-gray-600 dark:text-gray-400">
                              Free Items Value:
                            </div>
                            <div className="font-medium text-right text-purple-600 dark:text-purple-400">
                              LKR {Number(detailedTransactionData.free_items.total_value).toFixed(2)}
                            </div>
                            {detailedTransactionData?.free_items?.total_quantity > 0 && (
                              <>
                                <div className="text-gray-600 dark:text-gray-400">
                                  Free Item Transactions:
                                </div>
                                <div className="font-medium text-right text-purple-600 dark:text-purple-400">
                                  {detailedTransactionData.free_items.total_quantity}
                                </div>
                              </>
                            )}
                          </>
                        )}
                      </div>

                      {/* Detailed Transaction Breakdown */}
                      {detailedTransactionData && (
                        <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                          <h4 className="font-medium text-gray-700 dark:text-gray-200 mb-3">Transaction Details</h4>
                          
                          {/* Payment Methods */}
                          {detailedTransactionData.payment_methods && Object.keys(detailedTransactionData.payment_methods).length > 0 && (
                            <div className="mb-3">
                              <h5 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Payment Methods:</h5>
                              <div className="space-y-1">
                                {Object.entries(detailedTransactionData.payment_methods).map(([method, amount]) => (
                                  <div key={method} className="flex justify-between text-sm">
                                    <span className="capitalize">{method}:</span>
                                    <span className="font-medium">LKR {Number(amount).toFixed(2)}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Loyalty Redemptions */}
                          {detailedTransactionData.loyalty_redemptions && detailedTransactionData.loyalty_redemptions.total > 0 && (
                            <div className="mb-3">
                              <h5 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Loyalty Redemptions:</h5>
                              <div className="space-y-1">
                                <div className="flex justify-between text-sm">
                                  <span>Total Redeemed:</span>
                                  <span className="font-medium text-green-600 dark:text-green-400">
                                    LKR {Number(detailedTransactionData.loyalty_redemptions.total).toFixed(2)}
                                  </span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span>Transactions:</span>
                                  <span className="font-medium">{detailedTransactionData.loyalty_redemptions.count}</span>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Free Items */}
                          {detailedTransactionData.free_items && detailedTransactionData.free_items.total_quantity > 0 && (
                            <div className="mb-3">
                              <h5 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Free Items:</h5>
                              <div className="space-y-1">
                                <div className="flex justify-between text-sm">
                                  <span>Total Quantity:</span>
                                  <span className="font-medium text-purple-600 dark:text-purple-400">
                                    {detailedTransactionData.free_items.total_quantity}
                                  </span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span>Value:</span>
                                  <span className="font-medium text-purple-600 dark:text-purple-400">
                                    LKR {Number(detailedTransactionData.free_items.total_value).toFixed(2)}
                                  </span>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Discounts */}
                          {detailedTransactionData.discounts && detailedTransactionData.discounts.total > 0 && (
                            <div className="mb-3">
                              <h5 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Discounts:</h5>
                              <div className="space-y-1">
                                <div className="flex justify-between text-sm">
                                  <span>Total Discount:</span>
                                  <span className="font-medium text-orange-600 dark:text-orange-400">
                                    LKR {Number(detailedTransactionData.discounts.total).toFixed(2)}
                                  </span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span>Transactions:</span>
                                  <span className="font-medium">{detailedTransactionData.discounts.count}</span>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Returns */}
                          {detailedTransactionData.returns && detailedTransactionData.returns.total > 0 && (
                            <div className="mb-3">
                              <h5 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Returns:</h5>
                              <div className="space-y-1">
                                <div className="flex justify-between text-sm">
                                  <span>Total Returns:</span>
                                  <span className="font-medium text-red-600 dark:text-red-400">
                                    LKR {Number(detailedTransactionData.returns.total).toFixed(2)}
                                  </span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span>Transactions:</span>
                                  <span className="font-medium">{detailedTransactionData.returns.count}</span>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      <label className="flex items-center gap-2 mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                        <Coins
                          className="text-blue-500 dark:text-blue-400"
                          size={16}
                        />
                        Cash in Register (LKR)
                      </label>
                      <input
                        ref={inputRef}
                        type="number"
                        value={inputAmount}
                        onChange={(e) => setInputAmount(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="w-full p-3 transition-all bg-white border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:focus:ring-blue-400 dark:bg-gray-800 dark:text-white"
                        placeholder="Enter cash amount manually"
                      />
                      <button
                        onClick={handlePrintReport}
                        className="flex items-center justify-center w-full gap-2 px-4 py-2 mt-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                      >
                        <Printer size={16} /> Preview Mini Report
                      </button>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <label className="flex items-center gap-2 mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                        <Wallet
                          className="text-blue-500 dark:text-blue-400"
                          size={16}
                        />
                        Other Amount (LKR)
                      </label>
                      <input
                        type="number"
                        value={otherAmount}
                        onChange={(e) => setOtherAmount(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="w-full p-3 transition-all bg-white border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:focus:ring-blue-400 dark:bg-gray-800 dark:text-white"
                        placeholder="Enter other amount"
                      />
                    </motion.div>
                  </>
                ) : isUpdatingCash ? (
                  <>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                      className="p-4 bg-white border border-gray-100 shadow-sm dark:bg-gray-700 rounded-xl dark:border-gray-600"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="flex items-center gap-2 font-semibold text-gray-700 dark:text-gray-200">
                          <Coins
                            className="text-blue-500 dark:text-blue-400"
                            size={18}
                          />
                          Current Register Status
                        </h3>
                        {/* <button
                          onClick={fetchDetailedTransactionData}
                          disabled={loadingTransactionData}
                          className="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 disabled:opacity-50"
                        >
                          {loadingTransactionData ? "Loading..." : "Load Details"}
                        </button> */}
                      </div>
                      
                      {loadingTransactionData && (
                        <div className="mb-3 text-center">
                          <div className="inline-flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
                            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                            Loading transaction details...
                          </div>
                        </div>
                      )}
                      
                      <div className="grid grid-cols-2 gap-3">
                        <div className="text-gray-600 dark:text-gray-400">
                          Current Opening Cash:
                        </div>
                        <div className="font-medium text-right dark:text-white">
                          LKR {Number(registerStatus.openingCash || 0).toFixed(2)}
                        </div>
                        {inputAmount && (
                          <>
                            <div className="text-gray-600 dark:text-gray-400">
                              {addToCurrent ? "Preview Total:" : "New Amount:"}
                            </div>
                            <div className="font-medium text-right text-green-600 dark:text-green-400">
                              LKR {Number(getPreviewAmount()).toFixed(2)}
                            </div>
                          </>
                        )}
                        <div className="text-gray-600 dark:text-gray-400">
                          Total Sales:
                        </div>
                        <div className="font-medium text-right dark:text-white">
                          LKR {Number(registerStatus.totalSales || 0).toFixed(2)}
                        </div>
                        {detailedTransactionData?.payment_methods && Object.keys(detailedTransactionData.payment_methods).length > 0 && (
                          <>
                            <div className="col-span-2 text-xs text-gray-500 dark:text-gray-400 pt-1 pb-1 font-medium">Total Sales Breakdown:</div>
                            {Object.entries(detailedTransactionData.payment_methods).map(([method, amount]) => {
                              if (method === 'cash' && detailedTransactionData.cash_breakdown) {
                                // Show cash breakdown
                                return (
                                  <React.Fragment key={method}>
                                    <div className="text-gray-600 dark:text-gray-400 pl-2 text-xs capitalize">Cash Received:</div>
                                    <div className="font-medium text-right text-xs text-blue-700 dark:text-blue-300">LKR {Number(detailedTransactionData.cash_breakdown.received).toFixed(2)}</div>
                                    {detailedTransactionData.cash_breakdown.redemption > 0 && (
                                      <>
                                        <div className="text-gray-600 dark:text-gray-400 pl-2 text-xs capitalize">Cash Redemption:</div>
                                        <div className="font-medium text-right text-xs text-green-600 dark:text-green-400">LKR {Number(detailedTransactionData.cash_breakdown.redemption).toFixed(2)}</div>
                                      </>
                                    )}
                                    <div className="text-gray-600 dark:text-gray-400 pl-2 text-xs font-medium capitalize">Total Cash:</div>
                                    <div className="font-medium text-right text-xs font-medium text-blue-700 dark:text-blue-300">LKR {Number(detailedTransactionData.cash_breakdown.total).toFixed(2)}</div>
                                  </React.Fragment>
                                );
                              } else {
                                // Show other payment methods normally
                                return (
                                  <React.Fragment key={method}>
                                    <div className="text-gray-600 dark:text-gray-400 pl-2 text-xs capitalize">{method.replace(/_/g, ' ')}:</div>
                                    <div className="font-medium text-right text-xs text-blue-700 dark:text-blue-300">LKR {Number(amount).toFixed(2)}</div>
                                  </React.Fragment>
                                );
                              }
                            })}
                          </>
                        )}
                        {detailedTransactionData?.loyalty_redemptions?.total > 0 && (
                          <>
                            <div className="text-gray-600 dark:text-gray-400">
                              Loyalty Points Redeemed:
                            </div>
                            <div className="font-medium text-right text-green-600 dark:text-green-400">
                              LKR {Number(detailedTransactionData.loyalty_redemptions.total).toFixed(2)}
                              {detailedTransactionData.loyalty_redemptions.count > 0 && (
                                <span className="ml-2 text-xs text-gray-500">
                                  ({detailedTransactionData.loyalty_redemptions.count} transactions)
                                </span>
                              )}
                            </div>
                          </>
                        )}

                        {detailedTransactionData?.free_items?.total_value > 0 && (
                          <>
                            <div className="text-gray-600 dark:text-gray-400">
                              Free Items Value:
                            </div>
                            <div className="font-medium text-right text-purple-600 dark:text-purple-400">
                              LKR {Number(detailedTransactionData.free_items.total_value).toFixed(2)}
                            </div>
                            {detailedTransactionData?.free_items?.total_quantity > 0 && (
                              <>
                                <div className="text-gray-600 dark:text-gray-400">
                                  Free Item Transactions:
                                </div>
                                <div className="font-medium text-right text-purple-600 dark:text-purple-400">
                                  {detailedTransactionData.free_items.total_quantity}
                                </div>
                              </>
                            )}
                          </>
                        )}
                        <div className="text-gray-600 dark:text-gray-400">
                          Date:
                        </div>
                        <div className="font-medium text-right dark:text-white">
                          {registerStatus.currentDate || new Date().toISOString().split('T')[0]}
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      <div className="flex items-center gap-3 mb-3">
                        <input
                          type="checkbox"
                          id="addToCurrent"
                          checked={addToCurrent}
                          onChange={(e) => setAddToCurrent(e.target.checked)}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="addToCurrent" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Add to current opening cash
                        </label>
                      </div>

                      <label className="flex items-center gap-2 mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                        <Coins
                          className="text-blue-500 dark:text-blue-400"
                          size={16}
                        />
                        {addToCurrent ? "Amount to Add (LKR)" : "New Opening Cash Amount (LKR)"}
                      </label>
                      <input
                        ref={inputRef}
                        type="number"
                        min="0"
                        step="0.01"
                        value={inputAmount}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value === "" || parseFloat(value) >= 0) {
                            setInputAmount(value);
                          }
                        }}
                        onKeyDown={handleKeyDown}
                        className="w-full p-3 transition-all bg-white border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:focus:ring-blue-400 dark:bg-gray-800 dark:text-white"
                        placeholder={addToCurrent ? "Enter amount to add" : `Current: ${Number(registerStatus.openingCash || 0).toFixed(2)}`}
                      />
                      <div className="mt-2">
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {addToCurrent
                            ? `This amount will be added to current opening cash (${Number(registerStatus.openingCash || 0).toFixed(2)})`
                            : "Replace the current opening cash amount"
                          }
                        </p>
                        {inputAmount && (
                          <p className="text-sm font-medium text-green-600 dark:text-green-400">
                            {addToCurrent ? "New Total:" : "New Amount:"} LKR {Number(getPreviewAmount()).toFixed(2)}
                          </p>
                        )}
                      </div>
                    </motion.div>
                  </>
                ) : (
                  <>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                      className="text-center"
                    >
                      <motion.div
                        animate={{
                          scale: [1, 1.05, 1],
                          rotate: [0, 5, -5, 0],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          repeatDelay: 3,
                        }}
                        className="inline-flex items-center justify-center p-4 mb-4 bg-blue-100 rounded-full shadow-inner dark:bg-blue-900"
                      >
                        <LockOpen
                          className="text-blue-600 dark:text-blue-300"
                          size={28}
                        />
                      </motion.div>
                      <h3 className="mb-2 text-lg font-semibold text-gray-800 dark:text-white">
                        {isDailyInitialization
                          ? `Daily Register Setup - ${new Date().toISOString().split('T')[0]}`
                          : registerStatus.dailyInitialized
                          ? "Add More Opening Cash"
                          : "Register is currently closed"
                        }
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {isDailyInitialization
                          ? "Set up your register for today with the opening cash amount"
                          : registerStatus.dailyInitialized
                          ? `Current opening cash: LKR ${Number(registerStatus.openingCash || 0).toFixed(2)}`
                          : "Please enter the opening cash amount to start transactions"
                        }
                      </p>
                      {inputAmount && registerStatus.dailyInitialized && addToCurrent && (
                        <div className="p-2 mt-2 border border-green-200 rounded-lg bg-green-50 dark:bg-green-900/20 dark:border-green-800">
                          <p className="text-sm text-green-700 dark:text-green-300">
                            Preview Total: LKR {Number(getPreviewAmount()).toFixed(2)}
                          </p>
                        </div>
                      )}
                    </motion.div>

                    {registerStatus.dailyInitialized && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.15 }}
                      >
                        <div className="flex items-center gap-3 mb-3">
                          <input
                            type="checkbox"
                            id="addToCurrentOpening"
                            checked={addToCurrent}
                            onChange={(e) => setAddToCurrent(e.target.checked)}
                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <label htmlFor="addToCurrentOpening" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Add to current opening cash
                          </label>
                        </div>
                      </motion.div>
                    )}

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      <label className="flex items-center gap-2 mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                        <Coins
                          className="text-blue-500 dark:text-blue-400"
                          size={16}
                        />
                        {addToCurrent && registerStatus.dailyInitialized
                          ? "Amount to Add (LKR)"
                          : "Opening Cash Amount (LKR)"
                        }
                      </label>
                      <input
                        ref={inputRef}
                        type="number"
                        value={inputAmount}
                        onChange={(e) => setInputAmount(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="w-full p-3 transition-all bg-white border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:focus:ring-blue-400 dark:bg-gray-800 dark:text-white"
                        placeholder={
                          addToCurrent && registerStatus.dailyInitialized
                            ? "Enter amount to add"
                            : "Enter opening cash amount"
                        }
                      />
                      {addToCurrent && registerStatus.dailyInitialized && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            This amount will be added to current opening cash (LKR {Number(registerStatus.openingCash || 0).toFixed(2)})
                          </p>
                          {inputAmount && (
                            <p className="text-sm font-medium text-green-600 dark:text-green-400">
                              New Total: LKR {Number(getPreviewAmount()).toFixed(2)}
                            </p>
                          )}
                        </div>
                      )}
                    </motion.div>
                  </>
                )}

                {(errorMessage || registerError) && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-sm text-center text-red-500"
                  >
                    {errorMessage || registerError}
                  </motion.div>
                )}

                <motion.div
                  className="flex gap-3 pt-2"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <motion.button
                    whileHover={{ scale: 1.02, x: -2 }}
                    whileTap={{ scale: 0.98 }}
                    className="flex items-center justify-center flex-1 gap-2 p-3 text-gray-800 bg-white border border-gray-200 shadow-sm dark:bg-gray-700 dark:text-white rounded-xl hover:bg-gray-50 dark:hover:bg-gray-600 dark:border-gray-600"
                    onClick={onClose}
                    disabled={loading}
                  >
                    <X size={18} /> Cancel
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02, x: 2 }}
                    whileTap={{ scale: 0.98 }}
                    className="flex items-center justify-center flex-1 gap-2 p-3 text-white shadow-md bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl hover:opacity-90 disabled:opacity-50"
                    onClick={handleConfirm}
                    disabled={loading}
                  >
                    {loading ? (
                      "Processing..."
                    ) : (
                      <>
                        <Check size={18} />
                        {isClosing
                          ? "Close Register"
                          : isUpdatingCash
                          ? (addToCurrent ? "Add to Opening Cash" : "Update Opening Cash")
                          : isDailyInitialization
                          ? "Initialize Daily Register"
                          : addToCurrent && registerStatus.dailyInitialized
                          ? "Add to Opening Cash"
                          : "Open Register"
                        }
                      </>
                    )}
                  </motion.button>
                </motion.div>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default RegisterModal;
